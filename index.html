<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة نقاط البيع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-store"></i>
                </div>
                <h1>نظام إدارة نقاط البيع</h1>
                <p>أدخل كلمة المرور للوصول إلى النظام</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" id="passwordInput" placeholder="كلمة المرور" required>
                        <i class="fas fa-lock"></i>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول
                </button>
            </form>
            
            <div class="login-footer">
                <p>كلمة المرور الافتراضية: <strong>admin</strong></p>
            </div>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app hidden">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>نقاط البيع</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </li>
                    <li class="nav-item" data-page="sales">
                        <i class="fas fa-shopping-cart"></i>
                        <span>المبيعات</span>
                    </li>
                    <li class="nav-item" data-page="products">
                        <i class="fas fa-boxes"></i>
                        <span>المنتجات</span>
                    </li>
                    <li class="nav-item" data-page="categories">
                        <i class="fas fa-tags"></i>
                        <span>الأصناف</span>
                    </li>
                    <li class="nav-item" data-page="customers">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </li>
                    <li class="nav-item" data-page="suppliers">
                        <i class="fas fa-truck"></i>
                        <span>الموردين</span>
                    </li>
                    <li class="nav-item" data-page="purchases">
                        <i class="fas fa-shopping-bag"></i>
                        <span>المشتريات</span>
                    </li>
                    <li class="nav-item" data-page="debts">
                        <i class="fas fa-credit-card"></i>
                        <span>الديون</span>
                    </li>
                    <li class="nav-item" data-page="expenses">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>المصروفات</span>
                    </li>
                    <li class="nav-item" data-page="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <button class="logout-btn" onclick="handleLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </button>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="top-header">
                <div class="header-left">
                    <button class="btn btn-icon sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">لوحة التحكم</h1>
                </div>
                
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-label">المبيعات اليوم</span>
                            <span class="stat-value" id="headerTodaySales">٠.٠٠ ريال</span>
                        </div>
                    </div>
                    
                    <div class="header-actions">
                        <button class="btn btn-icon" onclick="toggleTheme()" title="تبديل الثيم">
                            <i class="fas fa-moon"></i>
                        </button>
                        <button class="btn btn-icon" onclick="showNotifications()" title="الإشعارات">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notificationBadge">0</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-wrapper">
                <div id="pageContent">
                    <!-- سيتم تحميل محتوى الصفحات هنا -->
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة التنبيه -->
    <div id="alertModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">تنبيه</h3>
            </div>
            <div class="modal-body">
                <p id="alertMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="hideAlertModal()">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">تأكيد</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideConfirmModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="handleConfirmYes()">موافق</button>
            </div>
        </div>
    </div>

    <script>
        // إضافة تأثيرات الحركة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة تسجيل الدخول
            const isLoggedIn = sessionStorage.getItem('erp_logged_in');

            if (isLoggedIn) {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
            } else {
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('mainApp').classList.add('hidden');
            }

            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });

            // التحكم في الشريط الجانبي للشاشات الصغيرة
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                });

                // إغلاق الشريط الجانبي عند النقر خارجه
                if (mainContent) {
                    mainContent.addEventListener('click', function() {
                        if (sidebar.classList.contains('open')) {
                            sidebar.classList.remove('open');
                        }
                    });
                }
            }
        });

        // دالة تسجيل الخروج
        function handleLogout() {
            if (typeof showConfirm === 'function') {
                showConfirm('هل أنت متأكد من تسجيل الخروج؟', function() {
                    sessionStorage.removeItem('erp_logged_in');
                    document.getElementById('loginScreen').classList.remove('hidden');
                    document.getElementById('mainApp').classList.add('hidden');
                    document.getElementById('passwordInput').value = '';
                });
            } else {
                // إذا لم تكن دالة showConfirm متاحة، استخدم confirm العادي
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('erp_logged_in');
                    document.getElementById('loginScreen').classList.remove('hidden');
                    document.getElementById('mainApp').classList.add('hidden');
                    document.getElementById('passwordInput').value = '';
                }
            }
        }
    </script>

    <!-- تحميل ملفات JavaScript -->
    <script src="database.js"></script>
    <script src="dashboard.js"></script>
    <script src="products.js"></script>
    <script src="categories.js"></script>
    <script src="sales.js"></script>
    <script src="customers.js"></script>
    <script src="suppliers.js"></script>
    <script src="purchases.js"></script>
    <script src="debts.js"></script>
    <script src="expenses.js"></script>
    <script src="reports.js"></script>
    <script src="settings.js"></script>
    <script src="main.js"></script>
</body>
</html>
