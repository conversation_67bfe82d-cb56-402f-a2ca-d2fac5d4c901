/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-light);
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.logo i {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-medium);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-md) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    color: var(--text-primary);
    font-weight: 500;
    text-decoration: none;
    margin-bottom: var(--spacing-xs);
    border-radius: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    border-bottom: 1px solid var(--border-light);
}

.nav-item:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
    position: relative;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: white;
}

.nav-item i {
    width: 24px;
    text-align: center;
    font-size: 1.2rem;
    margin-left: var(--spacing-sm);
}

.nav-item span {
    flex: 1;
    text-align: right;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
}

.user-info i {
    font-size: 1.2rem;
}

/* زر تسجيل الخروج */
.logout-btn {
    width: 100%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-family: inherit;
}

.logout-btn:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.logout-btn i {
    font-size: 1rem;
}

.logout-btn span {
    font-weight: 500;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    background: var(--bg-primary);
}

.content-wrapper {
    padding: var(--spacing-xl);
}

/* الشريط العلوي */
.top-header {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-glass);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-left h1 {
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
}

.sidebar-toggle {
    display: none;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-badge {
    position: absolute;
    top: -4px;
    left: -4px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

:root {
    /* الألوان الأساسية */
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;

    /* ألوان الخلفية */
    --bg-primary: #f1f5f9;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --bg-glass: rgba(255, 255, 255, 0.85);
    --bg-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);

    /* ألوان النص */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    
    /* الظلال */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
    --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.15);

    /* الحدود */
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
}

/* الثيم المظلم */
[data-theme="dark"] {
    --bg-primary: #1a1a2e;
    --bg-secondary: #16213e;
    --bg-card: #0f3460;
    --text-primary: #ffffff;
    --text-secondary: #b8c6db;
    --text-muted: #8892b0;
    --border-color: #2d3748;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* تحسينات عامة للتصميم */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-secondary);
}

*::-webkit-scrollbar {
    width: 8px;
}

*::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

*::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

*::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* مساعدات عامة */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* شاشة تسجيل الدخول */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--primary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.login-container {
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-light);
    padding: var(--spacing-xl) var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-glass);
    width: 100%;
    max-width: 450px;
    text-align: center;
}

.login-header .logo {
    width: 80px;
    height: 80px;
    background: var(--bg-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: 2rem;
    color: white;
}

.login-header h1 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 600;
}

.login-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.login-form {
    text-align: right;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.input-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.login-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--bg-gradient);
    color: white;
    border: 1px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    filter: brightness(1.1);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-success {
    background: var(--success-color);
    color: white;
    border: 1px solid transparent;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border: 1px solid transparent;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border: 1px solid transparent;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

.btn-info {
    background: var(--info-color);
    color: white;
    border: 1px solid transparent;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    filter: brightness(1.1);
}

.btn-block {
    width: 100%;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
}

.btn-icon:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* التطبيق الرئيسي */
.main-app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* شريط التنقل العلوي */
.top-navbar {
    background: var(--bg-card);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-md) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.navbar-menu {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.navbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    padding: var(--spacing-xl);
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-glass);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    background: var(--bg-secondary);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* لوحة المعلومات */
.dashboard-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.section-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title::before {
    content: '';
    width: 4px;
    height: 24px;
    background: var(--bg-gradient);
    border-radius: 2px;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-glass);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #66bb6a);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, var(--info-color), #42a5f5);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #ffb74d);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    box-shadow: var(--shadow-medium);
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* لوحة المعلومات الرئيسية */
.main-dashboard {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.dashboard-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.dashboard-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.card-content {
    padding: var(--spacing-lg);
}

/* إحصائيات المبيعات */
.sales-stats,
.inventory-stats,
.purchases-stats,
.debts-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.sales-item,
.inventory-item,
.purchase-item,
.debt-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.sales-item:last-child,
.inventory-item:last-child,
.purchase-item:last-child,
.debt-item:last-child {
    border-bottom: none;
}

.sales-item .label,
.inventory-item .label,
.purchase-item .label,
.debt-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.sales-item .value,
.inventory-item .value,
.purchase-item .value,
.debt-item .value {
    color: var(--text-primary);
    font-weight: 600;
}

.inventory-item.warning .value {
    color: var(--warning-color);
}

/* التنبيهات */
.alerts-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.alerts-section h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.alert-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.alert-item.warning {
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid rgba(255, 152, 0, 0.3);
    color: var(--warning-color);
}

.alert-item.info {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: var(--info-color);
}

.alert-item:hover {
    transform: translateX(-5px);
}

.no-alerts {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-lg);
}

/* آخر العمليات */
.recent-activities {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.recent-activities h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.activity-icon.success {
    background: var(--success-color);
}

.activity-icon.info {
    background: var(--info-color);
}

.activity-content {
    flex: 1;
}

.activity-message {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.activity-date {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.no-activities {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-lg);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .navbar-menu {
        display: none;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .login-container {
        padding: var(--spacing-xl);
        margin: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-row {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }
}

/* رأس الصفحة */
.page-header {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-glass);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page-title {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-title::before {
    content: '';
    width: 6px;
    height: 32px;
    background: var(--bg-gradient);
    border-radius: 3px;
}

.page-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* شريط البحث والفلترة */
.search-filter-bar {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.search-group {
    flex: 1;
    min-width: 300px;
}

.filter-group {
    display: flex;
    gap: var(--spacing-md);
}

.filter-group select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* الجداول */
.table-container {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-glass);
    border: 1px solid var(--border-light);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    color: var(--text-primary);
    font-weight: 700;
    padding: var(--spacing-lg) var(--spacing-xl);
    text-align: right;
    border-bottom: 2px solid var(--border-light);
    white-space: nowrap;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
    vertical-align: middle;
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: var(--bg-secondary);
    transform: scale(1.01);
    box-shadow: var(--shadow-light);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* معلومات المنتج في الجدول */
.product-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.product-info small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* شارات الحالة */
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.status-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-danger {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.action-buttons .btn {
    min-width: auto;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* إحصائيات المنتجات */
.products-stats {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-xl);
    justify-content: space-around;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 150px;
}

.stat-item .label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.stat-item .value {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

/* النماذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--bg-glass);
    backdrop-filter: blur(5px);
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-family: inherit;
    box-shadow: var(--shadow-light);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
    transform: translateY(-2px);
    background: var(--bg-secondary);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* النوافذ المنبثقة الكبيرة */
.large-modal {
    max-width: 800px;
    width: 95%;
}

/* تفاصيل المنتج */
.product-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 120px;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
    text-align: left;
}

/* صفحة المبيعات */
.sales-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    min-height: 70vh;
}

.products-section,
.invoice-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
}

/* تحسين الأقسام */
.dashboard-section {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-glass);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

/* شبكة المنتجات */
.products-grid {
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

.product-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.product-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
    line-height: 1.3;
}

.product-price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 0.9rem;
}

.product-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: 0.8rem;
}

.product-category {
    color: var(--text-muted);
    background: var(--border-color);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.product-stock {
    color: var(--text-secondary);
}

.product-actions {
    text-align: center;
}

.no-products {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
}

/* قسم الفاتورة */
.invoice-section {
    display: flex;
    flex-direction: column;
}

.customer-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.customer-info {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item .label {
    color: var(--text-secondary);
}

.info-item .value {
    color: var(--text-primary);
    font-weight: 600;
}

/* عناصر السلة */
.cart-items {
    flex: 1;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    max-height: 300px;
    overflow-y: auto;
}

.empty-cart {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.cart-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-secondary);
}

.cart-item:last-child {
    margin-bottom: 0;
}

.item-info h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.item-price {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.item-controls .quantity {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
}

.item-total {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* ملخص الفاتورة */
.invoice-summary {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.summary-row:last-child {
    margin-bottom: 0;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 700;
    padding-top: var(--spacing-sm);
    border-top: 2px solid var(--border-color);
    color: var(--primary-color);
}

.summary-row .label {
    color: var(--text-secondary);
}

.summary-row .value {
    color: var(--text-primary);
    font-weight: 600;
}

.summary-row.total .label,
.summary-row.total .value {
    color: var(--primary-color);
}

/* طرق الدفع */
.payment-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.payment-methods {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-sm);
}

.payment-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-primary);
}

.payment-method input[type="radio"] {
    margin: 0;
}

/* أزرار الفاتورة */
.invoice-actions {
    padding: var(--spacing-lg);
}

/* تفاصيل الفاتورة */
.invoice-details {
    font-family: 'Cairo', sans-serif;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.company-info h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.company-info p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

.invoice-info {
    text-align: left;
}

.invoice-info h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.invoice-info p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

/* جدول الفاتورة */
.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-xl);
}

.invoice-table th,
.invoice-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.invoice-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.invoice-table td {
    color: var(--text-secondary);
}

/* فلاتر التاريخ */
.history-filters {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.history-filters .filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.history-filters input[type="date"] {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    color: var(--text-primary);
}

/* تصميم متجاوب للمبيعات */
@media (max-width: 1024px) {
    .sales-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        max-height: 40vh;
    }
}

@media (max-width: 768px) {
    .invoice-header {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .invoice-info {
        text-align: right;
    }

    .payment-methods {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .cart-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .item-controls {
        justify-content: center;
    }

    .item-total {
        justify-content: space-between;
    }
}

/* أنماط العملاء */
.customers-stats {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-xl);
    justify-content: space-around;
    flex-wrap: wrap;
}

.balance-amount {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
}

.balance-negative {
    color: var(--danger-color);
    background: rgba(244, 67, 54, 0.1);
}

.balance-positive {
    color: var(--success-color);
    background: rgba(76, 175, 80, 0.1);
}

.balance-zero {
    color: var(--text-muted);
    background: var(--border-color);
}

.customer-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.customer-type.cash {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.customer-type.credit {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

/* تفاصيل العميل */
.customer-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.customer-info-section,
.customer-stats-section,
.customer-transactions-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.customer-info-section h4,
.customer-stats-section h4,
.customer-transactions-section h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    text-align: center;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

/* قائمة المعاملات */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.transaction-number,
.transaction-notes {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.transaction-amount {
    color: var(--danger-color);
    font-weight: 600;
    margin: 0 var(--spacing-md);
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-date {
    color: var(--text-muted);
    font-size: 0.8rem;
    min-width: 120px;
    text-align: left;
}

.no-transactions {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
    font-style: italic;
}

/* تصميم متجاوب للعملاء */
@media (max-width: 768px) {
    .customers-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .transaction-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .transaction-date {
        text-align: right;
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* أنماط المشتريات */
.purchases-stats,
.suppliers-stats {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-xl);
    justify-content: space-around;
    flex-wrap: wrap;
}

/* قسم عناصر فاتورة الشراء */
.purchase-items-section {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.items-header {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.items-list {
    max-height: 400px;
    overflow-y: auto;
}

.empty-items {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
    font-style: italic;
}

.purchase-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-card);
}

.purchase-item:last-child {
    border-bottom: none;
}

.item-fields {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--spacing-md);
    align-items: end;
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.field-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.field-group input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.field-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.item-total {
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-color);
}

/* ملخص فاتورة الشراء */
.purchase-summary {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    margin-top: var(--spacing-lg);
}

.purchase-summary .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.purchase-summary .summary-row:last-child {
    margin-bottom: 0;
}

.purchase-summary .summary-row.total {
    font-size: 1.1rem;
    font-weight: 700;
    padding-top: var(--spacing-sm);
    border-top: 2px solid var(--border-color);
    color: var(--primary-color);
}

.purchase-summary .label {
    color: var(--text-secondary);
}

.purchase-summary .value {
    color: var(--text-primary);
    font-weight: 600;
}

.purchase-summary .total .label,
.purchase-summary .total .value {
    color: var(--primary-color);
}

/* تفاصيل فاتورة الشراء */
.purchase-details {
    font-family: 'Cairo', sans-serif;
}

.purchase-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.purchase-info {
    text-align: left;
}

.purchase-info h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.purchase-info p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

.purchase-items {
    margin-bottom: var(--spacing-xl);
}

/* تصميم متجاوب للمشتريات */
@media (max-width: 768px) {
    .purchases-stats,
    .suppliers-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .item-fields {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .purchase-header {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .purchase-info {
        text-align: right;
    }
}

/* أنماط صفحة الديون */
.debts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.debt-stat .stat-icon {
    background: linear-gradient(135deg, var(--danger-color), #e57373);
}

.customers-stat .stat-icon {
    background: linear-gradient(135deg, var(--info-color), #64b5f6);
}

.payments-stat .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #81c784);
}

.overdue-stat .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #ffb74d);
}

/* مبلغ الدين */
.debt-amount {
    color: var(--danger-color);
    font-weight: 700;
}

.days-count {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.days-count.overdue {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

/* تحليل الديون */
.debts-analysis {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    margin-top: var(--spacing-xl);
}

.debts-analysis h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.analysis-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.analysis-card h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-weight: 600;
}

/* توزيع الديون */
.debt-distribution {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.distribution-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.distribution-item .label {
    color: var(--text-secondary);
    flex: 1;
}

.distribution-item .value {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0 var(--spacing-md);
}

.distribution-item .percentage {
    color: var(--primary-color);
    font-weight: 600;
    min-width: 50px;
    text-align: left;
}

/* قائمة أكبر المدينين */
.top-debtors-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.no-debtors {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-lg);
    font-style: italic;
}

.top-debtor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.debtor-info {
    flex: 1;
}

.debtor-name {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.debtor-phone {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.debtor-amount {
    color: var(--danger-color);
    font-weight: 700;
    font-size: 1.1rem;
}

/* تفاصيل دين العميل */
.customer-debt-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.debt-summary {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.debt-summary h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.summary-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-item .value {
    color: var(--text-primary);
    font-weight: 600;
}

.summary-item .debt-amount {
    color: var(--danger-color);
    font-weight: 700;
}

/* معاملات الدين */
.debt-transactions {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.debt-transactions h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.debt-transaction .transaction-amount {
    color: var(--danger-color);
}

.payment-transaction .transaction-amount {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--danger-color);
}

.transaction-amount.positive {
    color: var(--success-color);
}

/* معلومات دين العميل في الدفعة السريعة */
.customer-debt-info {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.info-row .value {
    color: var(--text-primary);
    font-weight: 600;
}

.info-row .debt-amount {
    color: var(--danger-color);
    font-weight: 700;
}

/* أنواع المدفوعات */
.payment-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.payment-type.customer_payment {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.payment-type.supplier_payment {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

/* تصميم متجاوب للديون */
@media (max-width: 768px) {
    .debts-stats {
        grid-template-columns: 1fr;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .distribution-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .distribution-item .percentage {
        text-align: right;
        min-width: auto;
    }

    .top-debtor-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .debtor-amount {
        text-align: center;
        font-size: 1.25rem;
    }
}

/* أنماط التقارير */
.reports-filters {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-group label {
    color: var(--text-secondary);
    font-weight: 500;
    white-space: nowrap;
}

.filter-group select,
.filter-group input[type="date"] {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.custom-period {
    transition: all 0.3s ease;
}

.custom-period.hidden {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

/* ملخص الأداء */
.performance-summary {
    margin-bottom: var(--spacing-xl);
}

.performance-summary h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.summary-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.summary-card.revenue::before {
    background: linear-gradient(90deg, var(--success-color), #81c784);
}

.summary-card.profit::before {
    background: linear-gradient(90deg, var(--info-color), #64b5f6);
}

.summary-card.expenses::before {
    background: linear-gradient(90deg, var(--warning-color), #ffb74d);
}

.summary-card.margin::before {
    background: linear-gradient(90deg, var(--primary-color), #9575cd);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.revenue .card-icon {
    background: linear-gradient(135deg, var(--success-color), #81c784);
}

.profit .card-icon {
    background: linear-gradient(135deg, var(--info-color), #64b5f6);
}

.expenses .card-icon {
    background: linear-gradient(135deg, var(--warning-color), #ffb74d);
}

.margin .card-icon {
    background: linear-gradient(135deg, var(--primary-color), #9575cd);
}

.card-content {
    flex: 1;
}

.card-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.card-content p {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.trend {
    font-size: 0.8rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

/* التقارير المفصلة */
.detailed-reports {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.reports-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tab-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.report-tab {
    padding: var(--spacing-xl);
}

.report-tab.hidden {
    display: none;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.report-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.report-stats .stat-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-stats .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.report-stats .value {
    color: var(--text-primary);
    font-weight: 600;
}

/* قوائم التقارير */
.products-list,
.customers-list,
.debtors-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
}

.product-item,
.customer-item,
.debtor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.product-info,
.customer-info {
    flex: 1;
}

.product-name,
.customer-name {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.product-quantity,
.customer-phone {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.product-total,
.customer-total {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
}

.no-data {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
    font-style: italic;
}

/* تحليل المخزون */
.inventory-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.analysis-section,
.low-stock-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.analysis-section h5,
.low-stock-section h5 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.category-item,
.low-stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.category-name,
.product-name {
    color: var(--text-primary);
    font-weight: 600;
}

.category-count,
.product-category {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.category-value {
    color: var(--primary-color);
    font-weight: 700;
}

.stock-info {
    text-align: left;
}

.current-stock {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.min-stock {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* تحليل العملاء */
.customers-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.top-customers,
.debtor-customers {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.top-customers h5,
.debtor-customers h5 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

/* التقرير المالي */
.financial-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.financial-section,
.cash-flow-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.financial-section h5,
.cash-flow-section h5 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.financial-table,
.cash-flow-table {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.financial-row,
.cash-flow-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.financial-row.total,
.cash-flow-row.total {
    font-weight: 700;
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.cash-flow-row.net-flow {
    font-size: 1.1rem;
    font-weight: 700;
    background: var(--bg-primary);
    border: 2px solid var(--primary-color);
}

.financial-row .label,
.cash-flow-row .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.financial-row.total .label,
.cash-flow-row.total .label,
.cash-flow-row.net-flow .label {
    color: inherit;
}

.financial-row .value,
.cash-flow-row .value {
    font-weight: 600;
}

.financial-row .value.positive,
.cash-flow-row .value.positive {
    color: var(--success-color);
}

.financial-row .value.negative,
.cash-flow-row .value.negative {
    color: var(--danger-color);
}

.cash-flow-section h6 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
}

.cash-flow-section:not(:last-child) {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

/* مخطط التقارير */
.report-chart {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
    text-align: center;
}

.report-chart canvas {
    max-width: 100%;
    height: auto;
}

/* أقسام التقارير */
.top-products,
.inventory-analysis,
.customers-analysis,
.financial-summary {
    margin-top: var(--spacing-xl);
}

.top-products h5 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

/* تصميم متجاوب للتقارير */
@media (max-width: 1024px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .inventory-analysis,
    .customers-analysis,
    .financial-summary {
        grid-template-columns: 1fr;
    }

    .reports-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: none;
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .reports-filters {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .filter-group {
        justify-content: space-between;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .report-stats {
        grid-template-columns: 1fr;
    }

    .reports-tabs {
        flex-direction: column;
    }

    .tab-btn {
        min-width: auto;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .report-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .products-list,
    .customers-list,
    .debtors-list {
        max-height: 200px;
    }

    .product-item,
    .customer-item,
    .debtor-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .product-total,
    .customer-total {
        text-align: center;
        font-size: 1.25rem;
    }

    .financial-row,
    .cash-flow-row {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .category-item,
    .low-stock-item {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: stretch;
    }

    .stock-info {
        text-align: center;
    }
}

/* أنماط الإعدادات */
.settings-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.settings-tab-btn {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    border-right: 1px solid var(--border-color);
}

.settings-tab-btn:last-child {
    border-right: none;
}

.settings-tab-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.settings-tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.settings-tab {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.settings-tab.hidden {
    display: none;
}

.settings-section {
    padding: var(--spacing-xl);
}

.settings-section h3 {
    margin: 0 0 var(--spacing-xl) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

/* شعار الشركة */
.logo-preview {
    margin-top: var(--spacing-md);
    text-align: center;
}

.logo-image {
    max-width: 200px;
    max-height: 100px;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
}

/* مربعات الاختيار المخصصة */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-primary);
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* النسخ الاحتياطي */
.backup-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.backup-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
}

.backup-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.backup-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.backup-card:nth-child(1) .backup-icon {
    background: linear-gradient(135deg, var(--success-color), #81c784);
}

.backup-card:nth-child(2) .backup-icon {
    background: linear-gradient(135deg, var(--warning-color), #ffb74d);
}

.backup-card:nth-child(3) .backup-icon {
    background: linear-gradient(135deg, var(--danger-color), #e57373);
}

.backup-content {
    flex: 1;
}

.backup-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-weight: 600;
}

.backup-content p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* معلومات النسخة الاحتياطية */
.backup-info {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.backup-info h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.info-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.info-item .value {
    color: var(--text-primary);
    font-weight: 600;
}

/* أوضاع خاصة */
.compact-mode {
    --spacing-xs: 2px;
    --spacing-sm: 4px;
    --spacing-md: 8px;
    --spacing-lg: 12px;
    --spacing-xl: 16px;
}

.compact-mode .sidebar {
    width: 200px;
}

.compact-mode .main-content {
    margin-right: 200px;
}

.compact-mode .page-header {
    padding: var(--spacing-md) var(--spacing-lg);
}

.compact-mode .stat-card {
    padding: var(--spacing-md);
}

.no-animations * {
    transition: none !important;
    animation: none !important;
}

/* تحسينات إضافية للاستجابة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 2000;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .sidebar-toggle {
        display: block !important;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-lg);
    }

    .top-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .header-stats {
        justify-content: center;
    }

    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .dashboard-section {
        margin-bottom: var(--spacing-lg);
    }
}

/* تصميم صفحة الأصناف */
.category-info {
    display: flex;
    align-items: center;
}

.category-info i {
    font-size: 1.2rem;
    margin-left: var(--spacing-sm);
}

.category-details {
    font-family: 'Cairo', sans-serif;
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-medium);
}

.category-info h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.category-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 1rem;
}

.products-section {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.products-section h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.products-list {
    display: grid;
    gap: var(--spacing-md);
}

.product-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.product-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.product-price {
    color: var(--success-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.product-quantity {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تحسينات للشارات */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

/* تحسينات للـ checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    font-weight: 500;
    color: var(--text-primary);
}

/* تصميم صفحة المصروفات */
.expense-description strong {
    color: var(--text-primary);
    font-weight: 600;
}

.expense-description small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.amount-cell {
    text-align: left;
    font-weight: 600;
    color: var(--danger-color);
}

.category-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-office {
    background: #e3f2fd;
    color: #1976d2;
}

.category-transport {
    background: #f3e5f5;
    color: #7b1fa2;
}

.category-utilities {
    background: #e8f5e8;
    color: #388e3c;
}

.category-maintenance {
    background: #fff3e0;
    color: #f57c00;
}

.category-marketing {
    background: #fce4ec;
    color: #c2185b;
}

.category-salary {
    background: #e1f5fe;
    color: #0277bd;
}

.category-rent {
    background: #f1f8e9;
    color: #689f38;
}

.category-insurance {
    background: #fff8e1;
    color: #ffa000;
}

.category-other {
    background: #f5f5f5;
    color: #616161;
}

.expense-details {
    font-family: 'Cairo', sans-serif;
}

.expense-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.expense-icon {
    width: 80px;
    height: 80px;
    background: var(--danger-color);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-medium);
}

.expense-info h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.expense-amount {
    margin: 0;
    color: var(--danger-color);
    font-size: 1.25rem;
    font-weight: 700;
}

/* تحسينات للنماذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .category-badge {
        font-size: 0.7rem;
        padding: 2px 6px;
    }

    .expense-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .expense-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* تصميم قسم تغيير كلمة المرور */
.security-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.security-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.security-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-medium);
}

.security-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.password-form {
    max-width: 500px;
}

.password-fields {
    margin-bottom: var(--spacing-xl);
}

.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group input {
    padding-left: 45px;
    flex: 1;
}

.password-toggle {
    position: absolute;
    left: 10px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 5px;
    border-radius: var(--border-radius);
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.password-strength {
    margin-bottom: var(--spacing-lg);
}

.strength-meter {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 4px;
}

.strength-bar.weak {
    background: var(--danger-color);
}

.strength-bar.medium {
    background: var(--warning-color);
}

.strength-bar.strong {
    background: var(--success-color);
}

.strength-text {
    font-size: 0.9rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.strength-text.weak {
    color: var(--danger-color);
}

.strength-text.medium {
    color: var(--warning-color);
}

.strength-text.strong {
    color: var(--success-color);
}

.password-requirements {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.password-requirements h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    padding: var(--spacing-xs) 0;
    color: var(--text-muted);
    position: relative;
    padding-right: 25px;
    transition: color 0.3s ease;
}

.password-requirements li::before {
    content: '✗';
    position: absolute;
    right: 0;
    color: var(--danger-color);
    font-weight: bold;
    transition: color 0.3s ease;
}

.password-requirements li.valid {
    color: var(--success-color);
}

.password-requirements li.valid::before {
    content: '✓';
    color: var(--success-color);
}

.form-actions {
    text-align: center;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .security-card {
        padding: var(--spacing-lg);
    }

    .security-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .security-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .password-form {
        max-width: 100%;
    }

    .password-input-group input {
        padding-left: 40px;
    }
}

/* تصميم الأدوات المتقدمة */
.tools-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: var(--border-radius-lg);
    color: white;
}

.tools-warning-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    display: block;
}

.tools-header h3 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.tools-warning {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.tool-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.tool-card.danger-card {
    border-color: var(--danger-color);
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.tool-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md) auto;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: var(--shadow-medium);
}

.danger-card .tool-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.tool-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.tool-content p {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.tool-card .btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.tool-card .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.tool-card .btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
}

.tool-card .btn-danger:hover {
    background: linear-gradient(135deg, #ee5a24 0%, #c44569 100%);
}

.tool-card .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.tool-card .btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.tool-card .btn-warning {
    background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
    border: none;
    color: white;
}

.tool-card .btn-warning:hover {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.tool-card .btn-secondary {
    background: linear-gradient(135deg, #78909c 0%, #607d8b 100%);
    border: none;
    color: white;
}

.tool-card .btn-secondary:hover {
    background: linear-gradient(135deg, #607d8b 0%, #546e7a 100%);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .tool-card {
        padding: var(--spacing-md);
    }

    .tool-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .tools-header {
        padding: var(--spacing-md);
    }

    .tools-warning-icon {
        font-size: 1.5rem;
    }

    .tools-header h3 {
        font-size: 1.25rem;
    }
}

/* السمة الداكنة */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-card: #3d3d3d;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #4d4d4d;
    --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* أحجام الخط */
[data-font-size="small"] {
    font-size: 14px;
}

[data-font-size="medium"] {
    font-size: 16px;
}

[data-font-size="large"] {
    font-size: 18px;
}

/* تصميم متجاوب للإعدادات */
@media (max-width: 768px) {
    .settings-tabs {
        flex-direction: column;
    }

    .settings-tab-btn {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .settings-tab-btn:last-child {
        border-bottom: none;
    }

    .backup-actions {
        grid-template-columns: 1fr;
    }

    .backup-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .compact-mode .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .compact-mode .main-content {
        margin-right: 0;
        margin-top: 0;
    }
}
